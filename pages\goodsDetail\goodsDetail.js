// pages/goodsDetail/goodsDetail.js
import * as GoodsApi from "~/api/GoodsApi.js"
import * as ShopCarApi from "~/api/ShopCarApi.js"

Page({
  data: {
    goodsId: '',
    swiperList: [],
    goodsInfo: {},
    currentPrice: 0,
    buyType: 0,
    isSpuSelectPopupShow: false,
    imageViewerIndex: 0,
    imageViewerVisible: false
  },
  doBuyNow(e) {
    this.handlePopupHide(() => {
      const params = e.detail;
      wx.navigateTo({ url: `/pages/order/settle/settle?goodsId=${params.goodsId}&goodsItemId=${params.goodsItemId}&buyNum=${params.buyNum}` });
    });
  },
  doAddCart(params) {
    // const params = e.detail;
    // wx.navigateTo({ url: `/pages/order/settle/settle?goodsId=${params.goodsId}&goodsItemId=${params.goodsItemId}&buyNum=${params.buyNum}` });
    this.handlePopupHide();
  },
  async init(id) {
    try {
      this.setData({ goodsId: id });
      const result = await GoodsApi.goodsInfo({ id });
      this.setData({ goodsInfo: result, swiperList: result.pic.split(",") });
      if (this.data.goodsInfo.specType === 1) {
        this.setData({ currentPrice: this.data.goodsInfo.goodsItemList[0].price });
      } else if (this.data.goodsInfo.specType === 2) {
        let price = 0;
        for (let i = 0; i < this.data.goodsInfo.goodsItemList.length; i++) {
          if (price == 0 || price > this.data.goodsInfo.goodsItemList[i].price) {
            price = this.data.goodsInfo.goodsItemList[i].price;
          }
        }
        this.setData({ currentPrice: price });
      }
    } catch (error) {
      console.error('err:', error);
    }
  },
  showSkuSelectPopup(type) {
    this.setData({ buyType: type || 0, isSpuSelectPopupShow: true });
    const goodsSpecsPopup = this.selectComponent('#goodsSpecsPopup');
    goodsSpecsPopup.init();
  },
  handlePopupHide(func) {
    this.setData({ isSpuSelectPopupShow: false, }, func);
  },
  toHome() {
    wx.navigateBack();
  },
  toCart() {
    wx.setStorageSync('tempSwitchTab', "/pages/home/<USER>/index");
    wx.navigateBack();
  },
  toBuyNow() {
    this.showSkuSelectPopup(1);
  },
  toAddCart() {
    this.showSkuSelectPopup(2);
  },
  picClick(e) {
    this.setData({ imageViewerIndex: e.detail.index, imageViewerVisible: true });
  },
  onCloseImageViewer() {
    this.setData({ imageViewerVisible: false });
  },
  onLoad(options) {
    this.init(options.goodsId);
  },
  onReady() {

  },
  onShow() {

  },
  onHide() {

  },
  onUnload() {

  }
})