// pages/order/list/orderList.js
import * as OrderApi from '~/api/OrderApi';
import { checkLogin } from '~/api/AuthApi';

Page({
  data: {
    tabIndex: 0,
    orderList: []
  },
  onRefresh({ detail: { isRefresh, current, size } }) {
    const params = {};
    if (this.data.tabIndex > 0) {
      params.status = this.data.tabIndex;
    }
    OrderApi.orderList({ ...params, current, size }).then(res => {
      if (isRefresh) {
        this.setData({ orderList: res.records });
      } else {
        this.setData({ orderList: this.data.orderList.concat(res.records) });
      }
      const useDrawer = this.selectComponent("#refresherLoader");
      useDrawer.complete({ total: res.total });
    }).catch(error => {
      console.log(error);
      wx.hideLoading();
      const useDrawer = this.selectComponent("#refresherLoader");
      useDrawer.completeError();
    });
  },
  toDetail(e) {
    const { item } = e.currentTarget.dataset;
    wx.navigateTo({ url: `/pages/order/orderDetail/orderDetail?orderCode=${item.orderCode}` });
  },
  // 付款
  doPay(e) {
    const { item } = e.currentTarget.dataset;
    wx.showLoading({ title: '正在请求...', mask: true });
    OrderApi.getWxPay({ orderCode: item.orderCode }).then(res => {
      console.log(res);
      wx.hideLoading();
      wx.requestPayment({
        timeStamp: res.timeStamp,
        nonceStr: res.nonceStr,
        package: res.packageValue,
        signType: res.signType,
        paySign: res.paySign,
        success: (res) => {
          console.log(res);
          wx.showToast({ title: '支付成功', icon: 'success', duration: 2000 });
          this.initRefresh();
        },
        fail: (res) => { console.log(res); }
      });
    }).catch(error => {
      console.log(error);
      wx.hideLoading();
    });
  },
  // 取消订单
  doCancelOrder(e) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: '确认取消订单？',
      success(res) {
        if (res.confirm) {
          const { item } = e.currentTarget.dataset;
          wx.showLoading({ title: '正在请求...', mask: true });
          OrderApi.orderCancel(item.orderCode).then(res => {
            wx.hideLoading();
            wx.showToast({ title: '操作成功', icon: 'success', duration: 2000 });
            _that.initRefresh();
          }).catch(error => {
            console.log(error);
            wx.hideLoading();
          });

        }
      }
    });
  },
  // 确认收货
  doTakeGoods(e) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: '确认完成订单？',
      success(res) {
        if (res.confirm) {
          const { item } = e.currentTarget.dataset;
          wx.showLoading({ title: '正在请求...', mask: true });
          OrderApi.orderTakeGoods(item.orderCode).then(res => {
            wx.hideLoading();
            wx.showToast({ title: '操作成功', icon: 'success', duration: 2000 });
            _that.initRefresh();
          }).catch(error => {
            console.log(error);
            wx.hideLoading();
          });

        }
      }
    });
  },
  initRefresh() {
    const useDrawer = this.selectComponent("#refresherLoader");
    useDrawer.doRefresh();
  },
  onTabsChange(e) {
    this.setData({ tabIndex: e.detail.value });
    this.initRefresh();
  },
  onLoad(options) {
    if (options.which) {
      this.setData({ tabIndex: options.which });
    } else {
      this.setData({ tabIndex: 0 });
    }
  },
  onReady() {
  },
  onShow() {
    checkLogin().then(res => {
      this.initRefresh();
    }).catch((e) => {
      wx.showModal({
        title: '提示',
        content: '当前未登录',
        confirmText: '前去登录',
        success(res) {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/auth/auth' });
          } else if (res.cancel) {
          }
        }
      });
    });
  }

})