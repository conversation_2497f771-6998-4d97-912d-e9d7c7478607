<!--pages/goodsDetail/goodsDetail.wxml-->
<wxs module="utils">
  var picList = function (pic) { return pic ? pic.split(",") : ""; }
  module.exports = { picList: picList }
</wxs>
<scroll-view scroll-y style="height: 100%;">
  <view class="flex-column" style="padding-bottom: 20rpx;">
    <view style="background-color: #fff;padding: 10rpx 20rpx;">
      <t-swiper height="400rpx" list="{{swiperList}}" navigation="{{ { type: 'dots-bar' } }}" bind:click="picClick" />
    </view>

    <view class="white-bg" style="margin-top: 20rpx;">
      <view>
        <text class="goods-detail-price1">￥</text>
        <text class="goods-detail-price2">{{ currentPrice }}</text>
      </view>
      <view class="goods-detail-name">{{ goodsInfo.name }}</view>
    </view>

    <!-- <view class="white-bg flex-row-center" style="margin-top: 20rpx;justify-content: space-between;">
      <view>
        <text style="color: #666;font-size: 28rpx;">规格: </text>
        <text style="color: #666;font-size: 28rpx;margin-left: 12rpx;">默认</text>
      </view>
      <t-icon name="chevron-right" size="38rpx" style="color: #999;" />
    </view> -->


    <view class="white-bg desc-content" style="margin-top: 20rpx;margin-bottom: 20rpx;">
      <view class="flex-row-center desc-content-title" style="justify-content: center;">
        <t-image t-class="img" src="/static/images/rec-left.jpg" />
        <span class="desc-content__title--text">详情介绍</span>
        <t-image t-class="img" src="/static/images/rec-right.jpg" />
      </view>
      <view style="margin-top: 20rpx;">
        <rich-text nodes="{{goodsInfo.goodsDesc||'无'}}"></rich-text>
      </view>

    </view>

    <view wx:if="{{utils.picList(goodsInfo.goodsDetailsPic).length > 0}}" wx:for="{{utils.picList(goodsInfo.goodsDetailsPic)}}" wx:key="index">
      <t-image t-class="desc-content__img" src="{{item}}" mode="widthFix" />
    </view>


  </view>
</scroll-view>

<view class="goods-bottom-operation">
  <view class="flex-row-center" style="justify-content: space-between;z-index: 6;">

    <view class="flex-row-center">
      <view bind:tap="toHome" class="flex-column-center" style="padding: 20rpx 10rpx 20rpx 10rpx;margin-left: 30rpx;">
        <t-icon name="home" size="38rpx" style="color: #666;" />
        <view class="operate-text">首页</view>
      </view>
      <view bind:tap="toCart" class="flex-column-center" style="padding: 20rpx 10rpx 20rpx 10rpx;margin-left: 20rpx;">
        <t-icon name="cart" size="38rpx" style="color: #666;" />
        <view class="operate-text">购物车</view>
      </view>
    </view>

    <view class="flex-row-center" style="margin-right: 30rpx;">
      <view class="bar-separately" bind:tap="toAddCart">加入购物车</view>
      <view class="bar-buy" bind:tap="toBuyNow">立即购买</view>
    </view>

  </view>
</view>

<goods-specs-popup id="goodsSpecsPopup" bind:doBuyNow="doBuyNow" bind:doAddCart="doAddCart" goodsInfo="{{goodsInfo}}" show="{{isSpuSelectPopupShow}}" bind:closeSpecsPopup="handlePopupHide">
</goods-specs-popup>

<t-image-viewer backgroundColor="rgba(10, 10, 10, 0.9)" closeBtn="{{true}}" bind:close="onCloseImageViewer" initialIndex="{{imageViewerIndex}}" showIndex="{{true}}" visible="{{imageViewerVisible}}" images="{{swiperList}}"></t-image-viewer>