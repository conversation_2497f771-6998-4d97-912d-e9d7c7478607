/* pages/goodsDetail/goodsDetail.wxss */
page {
  background-color: var(--pageBgColor);
  height: calc(100vh - env(safe-area-inset-bottom) - 115rpx);
}

.white-bg {
  background-color: #fff;
  box-sizing: content-box;
  padding: 20rpx 26rpx;
}

.goods-detail-price1 {
  font-size: 26rpx;
  color: #df3c39;
}

.goods-detail-price2 {
  font-size: 36rpx;
  color: #df3c39;
  margin-left: 6rpx;
  font-weight: bold;
}

.goods-detail-name {
  font-size: 34rpx;
  color: #333;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.desc-content__title--text {
  margin: 0 20rpx;
  color: #555;
  font-size: 26rpx;
}

.desc-content__img {
  width: 100%;
  height: auto;
}

.desc-content-title .img {
  width: 206rpx;
  height: 10rpx;
}

.goods-bottom-operation {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.1);
  z-index: 5;
}


.buy-bar-container {
  box-shadow: 0 0px 5px rgba(0, 0, 0, 0.2);
}

.operate-text {
  font-size: 22rpx;
  color: #666;
  margin-top: 8rpx;
}

.bar-separately,
.bar-buy {
  width: 190rpx;
  height: 70rpx;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
}

.bar-separately {
  background: linear-gradient(90deg, rgb(75, 145, 230) 0%, rgb(105, 165, 238) 100%);
  color: #fff;
  border-radius: 40rpx 0 0 40rpx;
}

.bar-buy {
  color: #fff;
  background: linear-gradient(90deg, rgb(19, 115, 231) 0%, rgb(81, 152, 238) 100%);
  border-radius: 0rpx 40rpx 40rpx 0rpx;
}