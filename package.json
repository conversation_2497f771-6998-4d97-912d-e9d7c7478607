{"name": "mall", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint --cache --fix --ext .js", "check": "node config/eslintCheck.js", "prepare": "husky install", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}, "dependencies": {"dayjs": "^1.11.13", "tdesign-miniprogram": "^1.9.1", "tslib": "^2.8.1"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "commitizen": "^4.3.1", "conventional-changelog-cli": "^5.0.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^3.5.3"}}