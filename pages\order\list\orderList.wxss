/* pages/order/list/orderList.wxss */
page {
  background-color: #f5f5f5;
  overflow: hidden;
  height: calc(100vh - env(safe-area-inset-bottom));
}

.refresher-loader-order {
  height: calc(100vh - env(safe-area-inset-bottom) - 100rpx) !important;
}

.order-item-layout {
  margin-top: 20rpx;
  padding: 20rpx 30rpx 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.order-item-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 60rpx;
}

.order-item-header .order-item-header-no {
  flex: 1;
  color: #666;
  font-size: 28rpx;
}

.order-item-header .order-item-header-status {
  color: #fa4126;
  font-size: 28rpx;
}

.order-item-goods-info__img {
  width: 130rpx;
  height: 130rpx;
}

.order-item-goods-info__img .order-item-goods-info__img_v {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.order-item-goods-info__content {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0rpx 20rpx;
}

.order-item-goods-info__content .order-item-goods-info__title {
  flex-shrink: 0;
  font-size: 30rpx;
  color: #333;
  line-height: 40rpx;
  font-weight: 600;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  -webkit-line-clamp: 2;
}

.order-item-goods-info__content .order-item-goods-info__desc {
  margin-top: 10rpx;
  color: #999;
  font-size: 24rpx;
}

.order-item-goods-info__right_content {
  display: flex;
  flex-direction: column;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.order-item-goods-info__right_content .order-item-goods-info__price {
  display: flex;
  flex-direction: column;
  text-align: right;
  font-size: 26rpx;
  color: #666;
}

.order-item-goods-info__right_content .order-item-goods-info__num {
  font-size: 26rpx;
  text-align: right;
  margin-top: 10rpx;
  color: #999;
}

.order-item-remark {
  font-size: 24rpx;
  margin-top: 12rpx;
  margin-left: 6rpx;
  color: #666;
}

.order-item-calc {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-end;
  margin-top: 12rpx;
}

.order-item-calc .order-item-calc-1 {
  color: #999;
  font-size: 26rpx;
  margin-right: 20rpx;
}

.order-item-calc .order-item-calc-2 {
  color: #555;
  font-size: 26rpx;
}

.btn-layout {
  justify-content: right;
  margin-top: 24rpx;
  justify-content: flex-end;
}

.btn-layout .order-list-btn {
  width: 150rpx;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 26rpx;
}

.wr-refresher {
  height: calc(100vh - 96rpx) !important;
}