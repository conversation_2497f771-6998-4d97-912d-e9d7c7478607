// pages/order/orderDetail/orderDetail.js
import * as OrderApi from "~/api/OrderApi.js"
import { getInvoicePromise } from '~/pages/home/<USER>/invoice/util';

Page({
  data: {
    orderDetail: {},
    address: {},
    goodsList: []
  },
  doTakeInvoice() {
    getInvoicePromise().then((invoice) => {
      this.showInvoiceConfirm(invoice, this.data.orderDetail.orderCode);
    }).catch(() => { });
    wx.navigateTo({ url: '/pages/home/<USER>/invoice/invoice?mode=select' })
  },
  doSaveInvoice() {
    wx.saveFileToDisk({
      filePath: this.data.orderDetail.orderInvoiceList[0].filePath,
      success(res) {
        console.log(res)
      },
      fail(res) {
        console.error(res)
      }
    })
  },
  showInvoiceConfirm(invoice, orderCode) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: `确认为"${invoice.titleName}"开票？`,
      success(res) {
        if (res.confirm) {
          wx.showLoading({ title: '正在请求...', mask: true });
          OrderApi.addInvoice({ id: invoice.id, orderCode: orderCode }).then(res => {
            wx.hideLoading();
            wx.showToast({ title: '操作成功', icon: 'success', duration: 2000 });
            _that.init(orderCode);
          }).catch(error => {
            console.log(error);
            wx.hideLoading();
          });

        }
      }
    });
  },
  // 付款
  doPay(e) {
    const { item } = e.currentTarget.dataset;
    wx.showLoading({ title: '正在请求...', mask: true });
    OrderApi.getWxPay({ orderCode: item.orderCode }).then(res => {
      console.log(res);
      wx.hideLoading();
      wx.requestPayment({
        timeStamp: res.timeStamp,
        nonceStr: res.nonceStr,
        package: res.packageValue,
        signType: res.signType,
        paySign: res.paySign,
        success: (res) => {
          console.log(res);
          wx.showToast({ title: '支付成功', icon: 'success', duration: 2000 });
          this.init(this.data.orderDetail.orderCode);
        },
        fail: (res) => { console.log(res); }
      });
    }).catch(error => {
      console.log(error);
      wx.hideLoading();
    });
  },
  // 取消订单
  doCancelOrder(e) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: '确认取消订单？',
      success(res) {
        if (res.confirm) {
          const { item } = e.currentTarget.dataset;
          wx.showLoading({ title: '正在请求...', mask: true });
          OrderApi.orderCancel(item.orderCode).then(res => {
            wx.hideLoading();
            wx.showToast({ title: '操作成功', icon: 'success', duration: 2000 });
            _that.init(item.orderCode);
          }).catch(error => {
            console.log(error);
            wx.hideLoading();
          });

        }
      }
    });
  },
  // 确认收货
  doTakeGoods(e) {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: '确认完成订单？',
      success(res) {
        if (res.confirm) {
          const { item } = e.currentTarget.dataset;
          wx.showLoading({ title: '正在请求...', mask: true });
          OrderApi.orderTakeGoods(item.orderCode).then(res => {
            wx.hideLoading();
            wx.showToast({ title: '操作成功', icon: 'success', duration: 2000 });
            _that.init(item.orderCode);
          }).catch(error => {
            console.log(error);
            wx.hideLoading();
          });

        }
      }
    });
  },
  async init(orderCode) {
    try {
      const result = await OrderApi.orderDetail(orderCode);
      this.setData({ orderDetail: result, address: result.orderDelivery, goodsList: result.orderItemList });
    } catch (error) {
      console.error('err:', error);
    }
  },
  onLoad(options) {
    this.init(options.orderCode);
  },
  onReady() {

  },
  onShow() {

  },
  onHide() {

  },
  onUnload() {

  }
})