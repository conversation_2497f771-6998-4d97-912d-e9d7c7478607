// pages/home/<USER>
import CarouselApi from "~/api/CarouselApi.js"
import GoodsApi from "~/api/GoodsApi.js"
import GoodsTypeApi from "~/api/GoodsTypeApi.js"
import BCApi from "~/api/BCApi.js"
// import { printNearestBuilding } from '~/utils/locationUtils' // 已隐藏距离计算逻辑
Page({
  data: {
    goodsList: [],
    menuList: [],
    swiperList: [],
    isSpuSelectPopupShow: false,
    goodsInfo: {},
    isAndroid: false,
    currentBuildingName: '',
    currentBuildingDistance: '',
    bcList: []
  },
  doBuyNow(e) {
    this.handlePopupHide(() => {
      const params = e.detail;
      wx.navigateTo({ url: `/pages/order/settle/settle?goodsId=${params.goodsId}&goodsItemId=${params.goodsItemId}&buyNum=${params.buyNum}` });
    });
  },
  doAddCart(params) {
    this.handlePopupHide();
  },
  handlePopupHide(func) {
    this.setData({ isSpuSelectPopupShow: false, }, func);
  },
  toAddGoods(e) {
    this.setData({ isSpuSelectPopupShow: true, goodsInfo: e.detail.goods });
    const goodsSpecsPopup = this.selectComponent('#goodsSpecsPopup');
    goodsSpecsPopup.init();
  },
  toPage(e) {
    const { item } = e.currentTarget.dataset;
    wx.setStorageSync('tempId', item.id);
    if (item.serviceGoods === 1) {
      wx.switchTab({ url: `/pages/home/<USER>/service` });
    } else {
      wx.switchTab({ url: `/pages/home/<USER>/index` });
    }
  },
  toService() {
    wx.switchTab({ url: `/pages/home/<USER>/service` });
  },

  // 跳转到搜索页
  gotoSearch() {
    wx.navigateTo({
      url: '/pages/search/index'
    });
  },
  async onLoad(options) {
    // 检测系统类型
    const systemInfo = wx.getDeviceInfo();
    const isAndroid = systemInfo.platform === 'android';
    this.setData({ isAndroid });

    // 初始化楼宇信息（内部会在楼宇信息确定后调用相关接口）
    await this.initBuildingInfo();

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },
  onShareAppMessage() {
    return {
      title: '楼宇企业全生命周期生态服务'
    };
  },
  onShareTimeline() {
    return {
      title: '楼宇企业全生命周期生态服务'
    };
  },
  onReady() {

  },
  onShow() {
    this.getTabBar().init();
    // 检查楼宇选择是否有更新
    this.updateBuildingName();
    // 刷新商品类型列表
    this.refreshGoodsTypeList();
  },

  // 加载页面数据（在楼宇信息初始化完成后调用）
  loadPageData() {
    const selectedBuildingId = wx.getStorageSync('selectedBuildingId');

    // 加载轮播图
    CarouselApi.list({ bcId: selectedBuildingId }).then(res => {
      this.setData({ swiperList: res.records.map(item => { return { value: item.pic, ariaLabel: item.name }; }) });
    }).catch(error => {
      console.error('加载轮播图失败:', error);
    });

    // 加载热门商品
    GoodsApi.searchHotGoods().then(res => {
      this.setData({ goodsList: res.records });
    }).catch(error => {
      console.error('加载热门商品失败:', error);
    });

    // 加载商品类型列表
    if (selectedBuildingId) {
      GoodsTypeApi.goodsTypeList({ showIndex: 1, bcId: selectedBuildingId }).then(res => {
        this.setData({ menuList: res.records });
      }).catch(error => {
        console.error('加载商品类型列表失败:', error);
      });
    } else {
      console.warn('selectedBuildingId为空，跳过goodsTypeList接口调用');
    }
  },

  // 初始化楼宇信息
  async initBuildingInfo() {
    try {
      // 获取楼宇列表
      const res = await BCApi.BCList();
      console.log('首页 BCList 接口返回数据:', res);

      // 兼容不同的数据结构
      let bcList = [];
      if (res && res.records && Array.isArray(res.records)) {
        bcList = res.records;
      } else if (res && Array.isArray(res)) {
        bcList = res;
      } else if (res && res.data && Array.isArray(res.data)) {
        bcList = res.data;
      } else {
        console.warn('首页 BCList 接口返回数据格式异常:', res);
        bcList = [];
      }

      this.setData({ bcList });

      // 获取当前选中的楼宇名称（隐藏距离计算逻辑）
      const savedBuildingName = wx.getStorageSync('selectedBuildingName');
      const savedBuildingId = wx.getStorageSync('selectedBuildingId');

      if (savedBuildingName) {
        this.setData({
          currentBuildingName: savedBuildingName,
          currentBuildingDistance: '' // 隐藏距离显示
        });

        // 楼宇信息已确定，可以调用依赖接口
        this.loadPageData();
      } else if (bcList.length > 0) {
        // 如果没有选中的楼宇，默认选择第一个楼宇
        await this.setDefaultBuilding(bcList);

        // 楼宇信息已确定，可以调用依赖接口
        this.loadPageData();
      } else {
        // 楼宇列表为空的情况
        console.warn('楼宇列表为空，无法初始化楼宇信息');
        // 仍然调用loadPageData，但goodsTypeList会因为没有bcId而跳过
        this.loadPageData();
      }
    } catch (error) {
      console.error('初始化楼宇信息失败:', error);
      this.setData({ bcList: [] });
      // 即使出错也要调用loadPageData，让其他不依赖楼宇ID的接口正常工作
      this.loadPageData();
    }
  },

  // 设置默认楼宇（使用第一个楼宇，隐藏距离计算）
  async setDefaultBuilding(bcList) {
    try {
      // 检查楼宇列表是否有效
      if (!bcList || !Array.isArray(bcList) || bcList.length === 0) {
        console.warn('楼宇列表为空，无法设置默认楼宇');
        return;
      }

      // 直接使用第一个楼宇作为默认值
      const firstBuilding = bcList[0];
      if (firstBuilding && firstBuilding.name) {
        this.setData({
          currentBuildingName: firstBuilding.name,
          currentBuildingDistance: '' // 隐藏距离显示
        });
        wx.setStorageSync('selectedBuildingId', firstBuilding.id);
        wx.setStorageSync('selectedBuildingName', firstBuilding.name);
        wx.setStorageSync('selectedBuildingDistance', ''); // 不保存距离信息
        console.log('使用第一个楼宇作为默认选择:', firstBuilding.name);
      }
    } catch (error) {
      console.log('设置默认楼宇失败:', error.message);
    }
  },

  // 更新楼宇名称（隐藏距离逻辑）
  updateBuildingName() {
    const savedBuildingName = wx.getStorageSync('selectedBuildingName');

    // 更新楼宇名称（如果有变化）
    if (savedBuildingName && savedBuildingName !== this.data.currentBuildingName) {
      this.setData({
        currentBuildingName: savedBuildingName,
        currentBuildingDistance: '' // 隐藏距离显示
      });
    }
  },

  // calculateBuildingDistance, getCurrentLocation, calculateDistance 方法已注释

  // 刷新商品类型列表
  refreshGoodsTypeList() {
    const selectedBuildingId = wx.getStorageSync('selectedBuildingId');
    if (selectedBuildingId) {
      GoodsTypeApi.goodsTypeList({ showIndex: 1, bcId: selectedBuildingId }).then(res => {
        this.setData({ menuList: res.records });
      }).catch(error => {
        console.error('刷新商品类型列表失败:', error);
      });
    }
  },

  // 楼宇选择器点击事件
  onBuildingSelectorTap() {
    wx.navigateTo({
      url: '/pages/building/list/buildingList',
      events: {
        // 监听楼宇选择事件
        buildingSelected: (building) => {
          this.setData({
            currentBuildingName: building.name,
            currentBuildingDistance: '' // 隐藏距离显示
          });
        }
      }
    });
  },
  onHide() {

  },
  onUnload() {

  }
})