// pages/cart/index.js
import { shopCarList, shopCarUpdateNum, shopCarUpdateSelected, shopCarUpdateSelectedAll, shopCarDelete, shopDeleteAll } from "~/api/ShopCarApi.js"

Page({
  data: {
    cartList: [],
    cartGroupData: {
      invalidGoodItems: [{}],
      isAllSelected: false, // 是否选择全部
      totalAmount: 15312, // 总价
      selectedGoodsCount: 1, // 选择结算的商品数
      totalDiscountAmount: 236 // 总优惠金额
    }
  },
  // 处理选中和取消选中商品
  handleSelectGoods(params) {
    const { goods, selected } = params.detail;
    this.data.cartList.forEach(item => { if (item.id == goods.id) { item.selected = selected; } });
    shopCarUpdateSelected(goods.id, selected);
    this.caclBar();
  },
  // 处理删除商品
  handleDeleteGoods(params) {
    const { goods } = params.detail;
    shopCarDelete(goods.id).then(res => { this.getCartList(); }).catch(error => { console.log(error); });
  },
  // 处理修改购买数量
  handleChangeNum(params) {
    const { goods, num } = params.detail;
    this.data.cartList.forEach(item => { if (item.id == goods.id) { item.goodsNum = num; } });
    shopCarUpdateNum(goods.id, num);
    this.caclBar();
  },
  // 处理全选
  handleSelectAll(params) {
    const { isAllSelected } = params.detail;
    this.data.cartList.forEach(item => { item.selected = isAllSelected; });
    this.setData({ cartList: this.data.cartList });
    shopCarUpdateSelectedAll(isAllSelected);
    this.caclBar();
  },
  // 处理底栏数据
  caclBar() {
    // 处理全选
    const flag = this.data.cartList.every(item => { return item.selected; });
    this.setData({ "cartGroupData.isAllSelected": flag });
    // 计算数量和价格
    const tempList = this.data.cartList.filter(item => { return item.selected; });
    let totalAmount = 0;
    let totalDiscountAmount = 0;
    let selectedGoodsCount = tempList.length;
    tempList.forEach(item => {
      totalAmount += item.goodsItem.price * item.goodsNum;
      totalDiscountAmount += (item.goodsItem.oldPrice - item.goodsItem.price) * item.goodsNum;
    });
    this.setData({
      "cartGroupData.totalAmount": totalAmount * 100,
      "cartGroupData.selectedGoodsCount": selectedGoodsCount,
      "cartGroupData.totalDiscountAmount": totalDiscountAmount * 100
    });
  },
  // 删除全部
  removeAll() {
    if (this.data.cartList.length > 0) {
      wx.showModal({
        title: '提示', content: '确定要删除全部吗？',
        success: (res) => {
          if (res.confirm) {
            shopDeleteAll().then(res => { this.getCartList(); }).catch(error => { console.log(error); });
          } else if (res.cancel) { }
        }
      });
    }
  },
  // 去结算
  onToSettle() {
    const tempList = this.data.cartList.filter(item => { return item.selected; });
    if (tempList.length > 0) {
      const cartIds = tempList.map(item => { return item.id; }).join(",");
      wx.navigateTo({ url: '/pages/order/settle/settle?cartIds=' + cartIds });
    }
  },
  getCartList() {
    shopCarList({}).then(res => {
      this.setData({ cartList: res });
      this.caclBar();
    }).catch(error => {
      console.log(error);
    });
  },
  onGotoHome() {
    wx.switchTab({ url: "/pages/home/<USER>/service" });
  },
  onLoad(options) {
    this.getCartList();
  },
  onReady() { },
  onShow() {
    this.getTabBar().init();
    this.getCartList();
  },

});
