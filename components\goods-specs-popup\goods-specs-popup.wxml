<!--components/goods-specs-popup/goods-specs-popup.wxml-->
<wxs module="utils">
  var getSelectedText = function (specList) {
    var result = "";
    for (var i = 0; i < specList.length; i++) {
      var spec = specList[i];
      for (var j = 0; j < specList[i].specItemList.length; j++) {
        var specitem = spec.specItemList[j];
        if (specitem.isSelected) {
          result += specitem.specItemName + ",";
        }
      }
    }
    if (result.length > 0) {
      result = result.substring(0, result.length - 1);
    }
    return result;
  }
  module.exports = { getSelectedText: getSelectedText };
</wxs>

<t-popup visible="{{show}}" placement="bottom" bind:visible-change="handlePopupHide">
  <view class="popup-container">
    <view class="popup-close" bindtap="handlePopupHide">
      <t-icon name="close" size="36rpx" />
    </view>

    <view class="flex-row" style="padding: 30rpx 28rpx 0 30rpx;">
      <t-image t-class="popup-sku-header__img" shape="round" src="{{imgSrc}}" />
      <!-- <image class="popup-sku-header__img" src="{{imgSrc}}" /> -->
      <view class="popup-sku-header__goods-info">
        <view class="popup-sku__goods-name">{{goodsInfo.name}}</view>
        <view class="goods-price-container">
          <text class="popup-sku__goods-price1">￥</text>
          <text class="popup-sku__goods-price2">{{ currentPrice }}</text>
        </view>
        <!-- 已选规格 -->
        <view class="flex-row-center popup-sku__selected-spec">
          <view>已选择：{{utils.getSelectedText(specList)}}</view>
          <!-- <block wx:for="{{specList}}" wx:key="specId">
            <view class="popup-sku__selected-item" wx:for="{{item.specItemList}}" wx:for-index="specItemIndex" wx:for-item="selectedItem" wx:if="{{selectedItem.isSelected}}" wx:key="specItemId">
              {{selectedItem.specItemName}}
            </view>
          </block> -->
        </view>
      </view>
    </view>

    <view class="popup-sku-body">
      <view class="popup-sku-group-container">
        <view class="popup-sku-row" wx:for="{{specList}}" wx:key="specId">
          <view class="popup-sku-row__title">{{item.specName}}</view>
          <block wx:for="{{item.specItemList}}" wx:for-item="specItem" wx:for-index="specItemIndex" wx:key="specItemId">
            <view bind:tap="toChooseItem" data-spec-index="{{index}}" data-spec-item-index="{{specItemIndex}}" class="popup-sku-row__item {{specItem.isSelected ? 'popup-sku-row__item--active' : ''}}">{{specItem.specItemName}} </view>
          </block>
        </view>
      </view>

      <view class="flex-row-center" style="justify-content: space-between;margin: 40rpx 0;">
        <view class="popup-sku__stepper-title">
          购买数量
          <!-- <view class="limit-text" wx:if="{{limitBuyInfo}}"> ({{limitBuyInfo}}) </view> -->
        </view>
        <t-stepper value="{{buyNum}}" theme="filled" bind:change="handleBuyNumChange" />
        <!-- <t-stepper value="{{buyNum}}" min="{{1}}" max="{{10}}" theme="filled" bind:change="handleBuyNumChange" /> -->
      </view>

    </view>

    <view class="flex-row-center" style="justify-content: center;">
      <view class="popup-sku__separately" bind:tap="toAddCart">加入购物车</view>
      <view class="popup-sku__buy" bind:tap="toBuyNow">立即购买</view>
    </view>

  </view>
</t-popup>
<t-toast id="t-toast" />