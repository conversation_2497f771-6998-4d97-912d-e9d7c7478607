
import { request } from "~/utils/http.js"

function addInvoice(params) {
  return request('/app/order/addInvoice', { ...params });
}
function orderCreate(params) {
  return request('/app/order/create', { ...params });
}
function getWxPay(params) {
  return request('/app/order/getWxPay', { ...params });
}

function orderList(params) {
  return request('/app/order/list', { ...params });
}
function orderDetail(orderCode) {
  return request('/app/order/info', { orderCode });
}
function orderCancel(orderCode) {
  return request('/app/order/cancel', { orderCode });
}
function orderTakeGoods(orderCode) {
  return request('/app/order/orderTakeGoods', { orderCode });
}

function getOrderNums() {
  return request('/app/order/getOrderNums', {});
}

module.exports = {
  orderCreate, getWxPay, orderList, orderCancel, orderDetail, orderTakeGoods, addInvoice, getOrderNums
}