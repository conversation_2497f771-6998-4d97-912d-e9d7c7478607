// pages/my/index.js
import { requestLoginInfo, checkLogin } from "~/api/AuthApi.js"
import { setUserInfo, clearAuth } from "~/utils/auth.js"
import * as OrderApi from "~/api/OrderApi.js"

Page({
  data: {
    userInfo: {
      avatar: '',
      nickName: '',
      mobile: '',
    },
    orderNums: { waitPay: 0, waitDelivery: 0, waitReceive: 0 },
    currAuthStep: 1,
  },
  logout() {
    const _that = this;
    wx.showModal({
      title: '提示',
      content: '确认退出登录吗？',
      success(res) {
        clearAuth();
        _that.setData({
          userInfo: { avatar: '', nickName: '', mobile: '', },
          currAuthStep: 1,
          orderNums: { waitPay: 0, waitDelivery: 0, waitReceive: 0 }
        });
        wx.showToast({ title: '成功退出登录', icon: 'success', duration: 2000 });
      }
    });
  },
  onLoad(options) {
  },
  onReady() {
  },
  onShow() {
    this.getTabBar().init();
    this.init();
  },
  onHide() {
  },
  onUnload() {
  },
  init() {
    this.setData({ orderNums: { waitPay: 0, waitDelivery: 0, waitReceive: 0 } });
    requestLoginInfo().then(res => {
      setUserInfo(res);
      this.setData({ currAuthStep: 3, userInfo: res });

      OrderApi.getOrderNums().then(res => {
        this.setData({ orderNums: res });
      }).catch(error => {
      });
    }).catch((e) => {
      this.setData({ currAuthStep: 1 });
    });
  },
  gotoUserAddress() {
    checkLogin().then(res => {
      wx.navigateTo({ url: '/pages/home/<USER>/address/address' })
    }).catch((e) => {
      wx.navigateTo({ url: '/pages/auth/auth' });
    });
  },
  gotoInvoice() {
    checkLogin().then(res => {
      wx.navigateTo({ url: '/pages/home/<USER>/invoice/invoice' })
    }).catch((e) => {
      wx.navigateTo({ url: '/pages/auth/auth' });
    });
  },
  gotoUserEditPage() {
    wx.navigateTo({ url: '/pages/home/<USER>/personInfo/personInfo' });
  },

  // 联系客服 - 使用企业微信客服
  gotoCustomerService() {
    // 检查API是否存在并且是函数
    if (wx.openCustomerServiceChat && typeof wx.openCustomerServiceChat === 'function') {
      wx.openCustomerServiceChat({
        extInfo: {
          url: 'https://work.weixin.qq.com/kfid/kfc14af0eb0453e516a' // 需要替换为实际的客服ID
        },
        corpId: 'ww36fad4ed63da23c7', // 需要替换为实际的企业ID
        success: (res) => {
          console.log('打开企业客服成功', res);
        },
        fail: (err) => {
          console.log('打开企业客服失败', err);
          // API调用失败时的备用方案
          this.showCustomerServiceOptions();
        }
      });
    } else {
      console.log('当前环境不支持 openCustomerServiceChat API');
      // API不存在时的备用方案
      this.showCustomerServiceOptions();
    }
  },

  // 显示客服联系方式选项
  showCustomerServiceOptions() {
    wx.showActionSheet({
      itemList: ['拨打客服电话', '查看联系方式'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 拨打电话
            wx.makePhoneCall({
              phoneNumber: '18975150553', // 替换为实际客服电话
              success: () => { console.log('拨打电话成功'); },
              fail: (err) => {
                console.log('拨打电话失败', err);
                wx.showToast({ title: '拨打失败', icon: 'none' });
              }
            });
            break;
          case 1:
            // 显示详细联系方式
            wx.showModal({
              title: '客服联系方式',
              content: '客服电话：18975150553\r\n工作时间：9:00-18:00\r\n\r\n如有问题请联系我们',
              showCancel: false,
              confirmText: '知道了'
            });
            break;
        }
      }
    });
  },
})