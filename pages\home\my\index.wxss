page {
  background: #f5f5f5;
  height: 100vh;
}

/* 底部安全区域 */
.safe-area-bottom {
  padding-bottom: calc(var(--tabbarHeight) + env(safe-area-inset-bottom));
}

/* 个人中心容器 */
.profile-container {
  min-height: calc(100vh - var(--tabbarHeight) - env(safe-area-inset-bottom));
  background: #f5f5f5;
  padding-top: 0;
  padding-bottom: calc(var(--tabbarHeight) + env(safe-area-inset-bottom));
}

/* 顶部头部区域 */
.profile-header {
  position: relative;
  padding-bottom: 40rpx;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 0 40rpx 40rpx;
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 20rpx 24rpx 0;
}

/* 统计卡片 */
.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-top: 24rpx;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
  flex: 1;
  text-align: center;
}

.stats-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666666;
}

.stats-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: #e0e0e0;
  margin: 0 24rpx;
}

/* 主要内容区域 */
.profile-content {
  padding: 0 24rpx 40rpx;
  margin-top: -20rpx;
  position: relative;
  z-index: 3;
}

/* 卡片区域 */
.section-card {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
}

/* 区域标题 */
.section-title {
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  position: relative;
  padding-left: 20rpx;
}

.title-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(135deg, var(--themeColor), #3486eb);
  border-radius: 3rpx;
}

/* 功能网格 */
.function-grid {
  display: flex;
  justify-content: space-around;
  padding: 32rpx;
}

.function-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  position: relative;
  transition: all 0.3s ease;
}

.function-item:active {
  transform: scale(0.95);
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.function-item:active .function-icon {
  background: #e9ecef;
}

.function-text {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

.function-badge {
  position: absolute;
  top: 16rpx;
  right: 20rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
  font-weight: 600;
}



/* 退出登录区域 */
.logout-section {
  margin-top: 40rpx;
}

.logout-btn {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.logout-btn:active {
  transform: scale(0.98);
  background: #f8f9fa;
}

.logout-text {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: 600;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-card {
  animation: slideInDown 0.6s ease 0.2s both;
}

.section-card {
  animation: fadeInUp 0.5s ease forwards;
}

.section-card:nth-child(1) { animation-delay: 0.3s; }
.section-card:nth-child(2) { animation-delay: 0.4s; }
.section-card:nth-child(3) { animation-delay: 0.5s; }

.logout-section {
  animation: fadeInUp 0.5s ease 0.6s both;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .header-content {
    padding: 32rpx 20rpx 0;
  }

  .stats-card {
    padding: 24rpx;
    margin-top: 20rpx;
  }

  .stats-number {
    font-size: 32rpx;
  }

  .stats-label {
    font-size: 22rpx;
  }

  .profile-content {
    padding: 0 20rpx 32rpx;
  }

  .section-title {
    padding: 28rpx 28rpx 20rpx;
  }

  .title-text {
    font-size: 30rpx;
  }

  .function-grid {
    padding: 28rpx;
  }

  .function-item {
    padding: 20rpx 0;
  }

  .function-icon {
    width: 72rpx;
    height: 72rpx;
  }



  .logout-section {
    padding: 0 20rpx;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200rpx) {
  .profile-container {
    max-width: 800rpx;
    margin: 0 auto;
  }
}



/* 特殊效果 */
.function-item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.function-item:active::before {
  width: 120rpx;
  height: 120rpx;
}

/* 统计数字动画 */
@keyframes countUp {
  from {
    transform: translateY(20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.stats-number {
  animation: countUp 0.8s ease 0.4s both;
}

.stats-item:nth-child(1) .stats-number { animation-delay: 0.4s; }
.stats-item:nth-child(3) .stats-number { animation-delay: 0.5s; }
.stats-item:nth-child(5) .stats-number { animation-delay: 0.6s; }

/* 悬浮效果 */
.section-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

/* 加载骨架屏 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}