/* pages/order/orderDetail/orderDetail.wxss */
page {
  background-color: #f5f5f5;
  height: calc(100vh - env(safe-area-inset-bottom));
}

page .divider-line {
  width: 100%;
  height: 20rpx;
  background-color: #f5f5f5;
}

.goods-info__img {
  width: 130rpx;
  height: 130rpx;
}

.goods-info__img .goods-info__img_v {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.goods-info__content {
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0rpx 20rpx;
}

.goods-info__content .goods-info__title {
  flex-shrink: 0;
  font-size: 30rpx;
  color: #333;
  line-height: 40rpx;
  font-weight: 600;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  -webkit-line-clamp: 2;
}

.goods-info__content .goods-info__desc {
  flex-shrink: 0;
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #666;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  -webkit-line-clamp: 1;
}

.goods-info__spec {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
}

.goods-info__right_content {
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.goods-info__right_content .goods-info__price {
  display: flex;
  flex-direction: column;
  text-align: right;
  font-size: 32rpx;
}

.goods-info__num {
  font-size: 26rpx;
  color: #777;
  text-align: right;
  margin-top: 10rpx;
  color: #999;
}


.bottom_submit_layout {
  width: 100%;
  height: 110rpx;
  position: fixed;
  left: 0;
  right: 0;
  bottom: env(safe-area-inset-bottom);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  background-color: white;
  border-top: 1rpx solid #F1F1F1;
  box-sizing: border-box;
}

.bottom_submit_layout .order-list-btn {
  width: 150rpx;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 26rpx;
  margin-right: 30rpx;
}

.remark-class {
  --td-font-size-base: 26rpx;
  --td-font-size-m: 30rpx;
  --td-input-label-text-color: #999;
  --td-input-vertical-padding: 20rpx 0rpx;
}

.remark-class .t-input__wrap .t-input__content {
  align-items: top;
  min-height: 96rpx;
}

.order-item-calc {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-end;
  margin-top: 12rpx;
  margin-bottom: 12rpx;
}

.order-item-calc .order-item-calc-1 {
  color: #999;
  font-size: 26rpx;
  margin-right: 20rpx;
}

.order-item-calc .order-item-calc-2 {
  color: #555;
  font-size: 26rpx;
}

.order-item-header-no {
  flex: 1;
  color: #666;
  font-size: 28rpx;
}

.order-item-header-status {
  color: #fa4126;
  font-size: 28rpx;
}

.label-layout {
  color: #666;
  padding: 20rpx 0;
  justify-content: space-between;
}