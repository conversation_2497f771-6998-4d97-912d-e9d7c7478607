<!--pages/order/list/orderList.wxml-->
<view class="flex-column">
  <t-tabs value="{{tabIndex}}" bind:change="onTabsChange">
    <t-tab-panel label="全部" value="0" />
    <t-tab-panel label="待付款" value="1" />
    <t-tab-panel label="待发货" value="2" />
    <!-- <t-tab-panel label="全部" value="0" badge-props="{{ { dot: true, offset: ['4px', '4px'] } }}" />
    <t-tab-panel label="待付款" value="1" badge-props="{{ { dot: true, offset: ['4px', '4px'] } }}" />
    <t-tab-panel label="待发货" value="2" badge-props="{{ { count: 8, offset: ['8px', '8px'] } }}" /> -->
    <t-tab-panel label="待收货" value="3" />
    <t-tab-panel label="已完成" value="4" />
  </t-tabs>

  <refresher-loader id="refresherLoader" wr-class="refresher-loader-order" dataList="{{orderList}}" bind:onRefresh="onRefresh">
    <view class="order-item-layout" wx:for="{{orderList}}" wx:key="orderCode" data-item="{{item}}" catch:tap="toDetail">
      <view class="order-item-header">
        <text class="order-item-header-no">订单号 {{item.orderCode}}</text>
        <text wx:if="{{item.orderStatus === 0}}" style="color: #FF9500;" class="order-item-header-status">待支付</text>
        <text wx:elif="{{item.orderStatus === 11}}" style="color: #3498DB;" class="order-item-header-status">待发货</text>
        <text wx:elif="{{item.orderStatus === 21}}" style="color: #F1C40F;" class="order-item-header-status">待收货</text>
        <text wx:elif="{{item.orderStatus === 50}}" style="color: #27AE60;" class="order-item-header-status">已完成</text>
        <text wx:elif="{{item.orderStatus === 1 || item.orderStatus === 12}}" style="color: #7F8C8D;" class="order-item-header-status">已取消</text>
      </view>

      <view class="flex-row" style="margin-top: 30rpx;" wx:for="{{item.orderItemList}}" wx:for-item="goods" wx:key="id">
        <view class="order-item-goods-info__img">
          <!-- <t-image t-class="order-item-goods-info__img_v" shape="round" src="{{goods.pic}}" lazy /> -->
          <image class="order-item-goods-info__img_v" src="{{goods.pic}}" />
        </view>
        <view class="order-item-goods-info__content">
          <view class="order-item-goods-info__title">{{goods.goodsName}}</view>
          <view class="order-item-goods-info__desc">规格: {{ goods.specDesc }}</view>
        </view>

        <view class="order-item-goods-info__right_content">
          <view class="order-item-goods-info__price">
            <price price="{{goods.price*100}}" />
          </view>
          <view class="order-item-goods-info__num">x {{goods.buyNum}}</view>
        </view>
      </view>

      <view class="order-item-remark lines-1">备注: {{item.remark||"无"}}</view>
      <view class="order-item-calc">
        <!-- <view class="order-item-calc-1">
          <text>总价 </text>
          <price price="{{item.orderPrice*100}}" fill decimalSmaller />
        </view> -->
        <!-- <view class="order-item-calc-1">
          <text>运费 </text>
          <price price="{{item.deliveryPrice*100}}" fill decimalSmaller />
        </view> -->
        <view class="order-item-calc-2">
          <text>总价: </text>
          <!-- <text>实付 </text> -->
          <price style="color: #fa4126;font-weight: bold;font-size: 30rpx;" price="{{item.orderPayPrice*100}}" />
        </view>
      </view>

      <view class="flex-row btn-layout">
        <view wx:if="{{item.orderStatus == 0 || item.orderStatus == 11}}" data-item="{{item}}" catch:tap="doCancelOrder" class="outline-btn order-list-btn" hover-class="hover-btn">取消订单</view>
        <view wx:if="{{item.orderStatus == 0}}" data-item="{{item}}" catch:tap="doPay" style="margin-left: 30rpx;" class="red-btn order-list-btn" hover-class="hover-btn">付款</view>
        <!-- <view wx:if="{{item.orderStatus == 50}}" data-item="{{item}}" catch:tap="doRebuy" style="margin-left: 30rpx;" class="theme-btn order-list-btn" hover-class="hover-btn">再次购买</view> -->
        <view wx:if="{{item.orderStatus == 21}}" data-item="{{item}}" catch:tap="doTakeGoods" style="margin-left: 30rpx;" class="theme-btn order-list-btn" hover-class="hover-btn">确认收货</view>
      </view>

    </view>
  </refresher-loader>
</view>