/* pages/building/list/buildingList.wxss */
.building-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f6f6f6;
}

/* 搜索框样式 */
.search-header {
  background-color: #ffffff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  height: 80rpx;
  background-color: #f4f3f3;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  min-width: 0;
  box-sizing: border-box;
}

.search-icon {
  margin-right: 20rpx;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  height: 100%;
  background: transparent;
  border: none;
  outline: none;
  font-size: 28rpx;
  color: #333333;
  min-width: 0;
  width: 100%;
}

.clear-icon {
  margin-left: 20rpx;
  flex-shrink: 0;
}

.search-placeholder {
  color: #999999;
  font-size: 28rpx;
}

/* 楼宇列表样式 */
.building-scroll {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

.building-list-content {
  padding: 20rpx 24rpx;
  padding-bottom: 40rpx;
}

.alphabet-section {
  margin-bottom: 32rpx;
}

.alphabet-title {
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  background-color: #f0f0f0;
  padding: 0 20rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  position: sticky;
  top: 0;
  z-index: 10;
}

.building-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  position: relative;
  overflow: hidden;
}

.building-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(36, 146, 242, 0.02) 0%, rgba(36, 146, 242, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 移除点击特效，改为选中状态反馈 */

.building-item.selected {
  border-color: #2492F2;
  background-color: #f8fbff;
  box-shadow: 0 4rpx 20rpx rgba(36, 146, 242, 0.15);
}

.building-item.selected::before {
  opacity: 1;
}

.building-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.building-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  line-height: 1.4;
  word-break: break-all;
}

.building-item.selected .building-name {
  color: #2492F2;
}

.building-address {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 12rpx;
  line-height: 1.4;
  word-break: break-all;
}

.building-distance {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999999;
}

.building-distance text {
  margin-left: 8rpx;
}

.building-check {
  margin-left: 24rpx;
  flex-shrink: 0;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  height: 60vh;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 24rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #cccccc;
  margin-top: 16rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .building-list-content {
    padding: 16rpx 20rpx;
  }

  .building-item {
    padding: 28rpx 20rpx;
    margin-bottom: 12rpx;
  }

  .building-name {
    font-size: 32rpx;
  }

  .building-address {
    font-size: 24rpx;
  }

  .building-distance {
    font-size: 22rpx;
  }
}

/* 滚动条隐藏 */
.building-scroll::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

/* 当前楼宇显示样式 */
.current-building {
  background-color: #ffffff;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.current-building-text {
  font-size: 26rpx;
  color: #666666;
  font-weight: 500;
}
